# 网络连接状态检测工具

这个工具包含三个Python脚本，专门用于检测内网IP范围***********-128的真实连接状态，特别是识别网线是否插入。

## 文件说明

### 1. ping_network_scanner.py (基础版)
- 传统的ping扫描工具
- 支持自定义IP范围、超时时间、并发数
- 适用于基本的网络连通性测试

### 2. simple_ping_scanner.py (综合版) ⭐推荐
- **多重检测方法**: Ping + 端口扫描 + ARP检测
- **更准确的连接状态判断**: 能识别网线物理连接
- **智能设备识别**: 根据开放端口推测设备类型
- **详细的连接状态分析**: 区分在线、有服务、物理连接等状态

### 3. advanced_network_detector.py (高级版)
- **最全面的检测**: TCP/UDP扫描 + ARP检测 + 可信度评估
- **精确的设备状态判断**: 使用可信度算法综合评估
- **详细的设备分类**: 准确识别设备类型和服务
- **专业级报告**: 提供网线连接状态的专业分析

## 使用方法

### 🔍 检测网线连接状态 (推荐)
```bash
python simple_ping_scanner.py
```

### 🔬 高级设备检测
```bash
python advanced_network_detector.py
```

### 📡 基础ping扫描
```bash
python ping_network_scanner.py
```

## 输出结果

### 控制台输出
- **实时进度显示**: 显示检测进度和每个IP的状态
- **智能状态标识**:
  - ✓ 设备在线
  - ◐ 有服务运行但不响应ping
  - ◑ 检测到物理连接(MAC地址)
  - ✗ 无任何连接
- **详细统计摘要**: 网线连接状态分析和设备分类

### CSV文件输出
自动生成详细的CSV报告，包含：

#### 综合版 (simple_ping_scanner.py)
- IP地址、Ping状态、响应时间、开放端口
- MAC地址、设备类型推测、连接状态、扫描时间

#### 高级版 (advanced_network_detector.py)
- 所有综合版信息 + TCP/UDP端口详情
- 可信度评分、设备状态等级

文件名格式：`network_scan_results_YYYYMMDD_HHMMSS.csv`

## 系统要求

- Python 3.6+
- Windows/Linux/macOS系统
- 网络连接权限

## 特性

### 完整版特性
- 多线程并发扫描(默认50个线程)
- 可自定义IP范围和超时时间
- 详细的错误信息记录
- 响应时间精确测量
- 完整的统计报告

### 简化版特性
- 30个并发线程
- 固定扫描***********-128
- 简洁的输出格式
- 快速扫描

## 示例输出

```
网络Ping扫描工具
==================================================
开始扫描网络范围: ***********-128
超时设置: 3秒
并发线程数: 50
--------------------------------------------------
[  0.8%] ✗ *********** - Failed
[  1.6%] ✓ *********** - Success
[  2.3%] ✗ *********** - Failed
...
--------------------------------------------------
扫描完成! 总计: 128, 成功: 15

==================================================
扫描结果摘要:
==================================================
总计IP数量: 128
成功响应: 15 (11.7%)
无响应: 110 (85.9%)
超时: 3 (2.3%)
错误: 0 (0.0%)

活跃主机 (15个):
***********     ***********     ***********0    ************
...

扫描耗时: 12.34秒
结果已保存到: ping_scan_results_20241201_143022.csv
```

## 注意事项

1. 确保有足够的网络权限执行ping命令
2. 防火墙可能会阻止ping请求
3. 某些设备可能配置为不响应ping请求
4. 扫描大量IP时请注意网络负载

## 自定义配置

### 修改IP范围 (完整版)
```python
scanner = NetworkPingScanner(
    base_ip="192.168.1",    # 修改基础IP
    start_host=1,           # 起始主机号
    end_host=254,           # 结束主机号
    timeout=5               # 超时时间(秒)
)
```

### 修改并发数
```python
scanner.scan_network(max_workers=100)  # 增加并发线程数
```

## 故障排除

### 常见问题
1. **权限不足**: 在某些系统上可能需要管理员权限
2. **网络不通**: 检查网络连接和路由配置
3. **防火墙阻止**: 确保防火墙允许ping请求
4. **Python版本**: 确保使用Python 3.6或更高版本

### 解决方案
- Windows: 以管理员身份运行命令提示符
- Linux: 使用sudo运行脚本
- 检查网络配置和防火墙设置
