# 网络Ping扫描工具

这个工具包含两个Python脚本，用于扫描内网IP范围***********-128，并将结果保存为CSV文件。

## 文件说明

### 1. ping_network_scanner.py (完整版)
- 功能丰富的网络扫描工具
- 支持自定义IP范围、超时时间、并发数
- 详细的错误处理和进度显示
- 生成详细的扫描报告

### 2. simple_ping_scanner.py (简化版)
- 简单易用的扫描工具
- 专门针对***********-128范围
- 代码简洁，易于理解和修改

## 使用方法

### 运行完整版扫描器
```bash
python ping_network_scanner.py
```

### 运行简化版扫描器
```bash
python simple_ping_scanner.py
```

## 输出结果

### 控制台输出
- 实时显示扫描进度
- 显示每个IP的ping结果
- 扫描完成后显示统计摘要

### CSV文件
自动生成带时间戳的CSV文件，包含以下列：
- IP地址
- 状态 (在线/离线/超时)
- 响应时间(毫秒)
- 扫描时间

文件名格式：`ping_scan_results_YYYYMMDD_HHMMSS.csv`

## 系统要求

- Python 3.6+
- Windows/Linux/macOS系统
- 网络连接权限

## 特性

### 完整版特性
- 多线程并发扫描(默认50个线程)
- 可自定义IP范围和超时时间
- 详细的错误信息记录
- 响应时间精确测量
- 完整的统计报告

### 简化版特性
- 30个并发线程
- 固定扫描***********-128
- 简洁的输出格式
- 快速扫描

## 示例输出

```
网络Ping扫描工具
==================================================
开始扫描网络范围: ***********-128
超时设置: 3秒
并发线程数: 50
--------------------------------------------------
[  0.8%] ✗ *********** - Failed
[  1.6%] ✓ *********** - Success
[  2.3%] ✗ *********** - Failed
...
--------------------------------------------------
扫描完成! 总计: 128, 成功: 15

==================================================
扫描结果摘要:
==================================================
总计IP数量: 128
成功响应: 15 (11.7%)
无响应: 110 (85.9%)
超时: 3 (2.3%)
错误: 0 (0.0%)

活跃主机 (15个):
***********     ***********     ***********0    ************
...

扫描耗时: 12.34秒
结果已保存到: ping_scan_results_20241201_143022.csv
```

## 注意事项

1. 确保有足够的网络权限执行ping命令
2. 防火墙可能会阻止ping请求
3. 某些设备可能配置为不响应ping请求
4. 扫描大量IP时请注意网络负载

## 自定义配置

### 修改IP范围 (完整版)
```python
scanner = NetworkPingScanner(
    base_ip="192.168.1",    # 修改基础IP
    start_host=1,           # 起始主机号
    end_host=254,           # 结束主机号
    timeout=5               # 超时时间(秒)
)
```

### 修改并发数
```python
scanner.scan_network(max_workers=100)  # 增加并发线程数
```

## 故障排除

### 常见问题
1. **权限不足**: 在某些系统上可能需要管理员权限
2. **网络不通**: 检查网络连接和路由配置
3. **防火墙阻止**: 确保防火墙允许ping请求
4. **Python版本**: 确保使用Python 3.6或更高版本

### 解决方案
- Windows: 以管理员身份运行命令提示符
- Linux: 使用sudo运行脚本
- 检查网络配置和防火墙设置
