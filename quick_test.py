#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
测试少量IP来验证检测方法的有效性
"""

import subprocess
import socket
import time
from datetime import datetime
import os

def quick_test_ip(ip):
    """快速测试单个IP的多种检测方法"""
    print(f"\n正在测试 {ip}:")
    print("-" * 40)
    
    results = {}
    
    # 1. Ping测试
    try:
        if os.name == 'nt':
            cmd = ['ping', '-n', '1', '-w', '2000', ip]
        else:
            cmd = ['ping', '-c', '1', '-W', '2', ip]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3)
        end_time = time.time()
        
        if result.returncode == 0:
            response_time = round((end_time - start_time) * 1000, 2)
            results['ping'] = f"✓ 响应 ({response_time}ms)"
        else:
            results['ping'] = "✗ 无响应"
    except:
        results['ping'] = "✗ 超时"
    
    # 2. 端口扫描
    common_ports = [22, 23, 80, 135, 139, 443, 445, 3389]
    open_ports = []
    
    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((ip, port))
            if result == 0:
                open_ports.append(port)
            sock.close()
        except:
            pass
    
    if open_ports:
        results['ports'] = f"✓ 开放端口: {','.join(map(str, open_ports))}"
    else:
        results['ports'] = "✗ 无开放端口"
    
    # 3. ARP检测
    try:
        if os.name == 'nt':
            arp_result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=3)
        else:
            arp_result = subprocess.run(['arp', '-n'], capture_output=True, text=True, timeout=3)
        
        mac_found = False
        if arp_result.returncode == 0:
            lines = arp_result.stdout.split('\n')
            for line in lines:
                if ip in line:
                    parts = line.split()
                    for part in parts:
                        if ('-' in part and len(part) == 17) or (':' in part and len(part) == 17):
                            results['arp'] = f"✓ MAC地址: {part}"
                            mac_found = True
                            break
                    if mac_found:
                        break
        
        if not mac_found:
            results['arp'] = "✗ 未找到MAC地址"
    except:
        results['arp'] = "✗ ARP检测失败"
    
    # 显示结果
    for test_type, result in results.items():
        print(f"{test_type.upper():8}: {result}")
    
    # 综合判断
    ping_ok = "✓" in results['ping']
    ports_ok = "✓" in results['ports']
    arp_ok = "✓" in results['arp']
    
    if ping_ok and ports_ok:
        status = "🟢 设备完全在线"
    elif ping_ok:
        status = "🟡 设备在线但无服务"
    elif ports_ok:
        status = "🟠 有服务但不响应ping"
    elif arp_ok:
        status = "🔵 物理连接存在"
    else:
        status = "🔴 很可能无设备或未连接"
    
    print(f"综合判断: {status}")
    
    return {
        'ip': ip,
        'ping': ping_ok,
        'ports': ports_ok,
        'arp': arp_ok,
        'status': status
    }

def main():
    """主函数 - 测试几个IP地址"""
    print("网络检测方法快速验证")
    print("=" * 50)
    print("测试范围: ***********-10 (前10个IP)")
    print("目的: 验证检测方法的有效性")
    print("=" * 50)
    
    test_ips = [f"10.70.230.{i}" for i in range(1, 11)]
    results = []
    
    start_time = time.time()
    
    for ip in test_ips:
        result = quick_test_ip(ip)
        results.append(result)
        time.sleep(0.5)  # 避免过快请求
    
    end_time = time.time()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("快速测试汇总:")
    print("=" * 50)
    
    total = len(results)
    ping_count = sum(1 for r in results if r['ping'])
    ports_count = sum(1 for r in results if r['ports'])
    arp_count = sum(1 for r in results if r['arp'])
    
    print(f"测试IP数量: {total}")
    print(f"Ping响应: {ping_count} ({ping_count/total*100:.0f}%)")
    print(f"有开放端口: {ports_count} ({ports_count/total*100:.0f}%)")
    print(f"检测到MAC: {arp_count} ({arp_count/total*100:.0f}%)")
    
    print(f"\n各IP状态:")
    for result in results:
        print(f"  {result['ip']} - {result['status']}")
    
    print(f"\n测试耗时: {end_time - start_time:.1f}秒")
    
    # 给出建议
    print(f"\n建议:")
    if ping_count > 0 or ports_count > 0:
        print("✓ 检测方法有效，发现了活跃设备")
        print("✓ 可以运行完整扫描: python simple_ping_scanner.py")
    else:
        print("⚠ 前10个IP都无响应，可能:")
        print("  1. 该网段确实没有设备")
        print("  2. 网络配置问题")
        print("  3. 防火墙阻止了检测")
        print("建议检查网络配置后再运行完整扫描")

if __name__ == "__main__":
    main()
