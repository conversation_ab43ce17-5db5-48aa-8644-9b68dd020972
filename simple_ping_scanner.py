#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接状态检测工具
检测***********-128的网络连接状态并生成CSV报告
包含ping测试、端口扫描、ARP检测等多种检测方法
"""

import subprocess
import csv
import time
from datetime import datetime
import os
import socket
import threading
from concurrent.futures import ThreadPoolExecutor

def check_port_connectivity(ip, ports=[22, 23, 80, 135, 139, 443, 445, 3389], timeout=1):
    """
    检测常用端口连通性

    Args:
        ip (str): IP地址
        ports (list): 要检测的端口列表
        timeout (int): 连接超时时间

    Returns:
        list: 开放的端口列表
    """
    open_ports = []
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((ip, port))
            if result == 0:
                open_ports.append(port)
            sock.close()
        except:
            pass
    return open_ports

def check_arp_table(ip):
    """
    检查ARP表中是否存在该IP

    Args:
        ip (str): IP地址

    Returns:
        str: MAC地址或空字符串
    """
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=5)
        else:  # Linux/Unix
            result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=5)

        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if ip in line:
                    # 提取MAC地址
                    parts = line.split()
                    for part in parts:
                        if '-' in part and len(part) == 17:  # Windows格式 xx-xx-xx-xx-xx-xx
                            return part
                        elif ':' in part and len(part) == 17:  # Linux格式 xx:xx:xx:xx:xx:xx
                            return part
        return ""
    except:
        return ""

def comprehensive_network_check(ip, timeout=3):
    """
    综合网络检测

    Args:
        ip (str): IP地址
        timeout (int): 超时时间(秒)

    Returns:
        dict: 详细的检测结果
    """
    result = {
        'IP地址': ip,
        'Ping状态': '离线',
        'Ping响应时间(ms)': 0,
        '开放端口': '',
        'MAC地址': '',
        '设备类型推测': '',
        '连接状态': '无连接',
        '扫描时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    # 1. Ping测试
    try:
        if os.name == 'nt':  # Windows
            cmd = ['ping', '-n', '1', '-w', str(timeout * 1000), ip]
        else:  # Linux/Unix
            cmd = ['ping', '-c', '1', '-W', str(timeout), ip]

        start_time = time.time()
        ping_result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 1)
        end_time = time.time()

        if ping_result.returncode == 0:
            result['Ping状态'] = '在线'
            result['Ping响应时间(ms)'] = round((end_time - start_time) * 1000, 2)
            result['连接状态'] = '网络连通'
    except:
        pass

    # 2. 端口扫描
    open_ports = check_port_connectivity(ip, timeout=1)
    if open_ports:
        result['开放端口'] = ','.join(map(str, open_ports))
        result['连接状态'] = '有服务运行'

        # 根据开放端口推测设备类型
        if 3389 in open_ports:
            result['设备类型推测'] = 'Windows服务器/PC'
        elif 22 in open_ports:
            result['设备类型推测'] = 'Linux/Unix服务器'
        elif 80 in open_ports or 443 in open_ports:
            result['设备类型推测'] = 'Web服务器'
        elif 135 in open_ports or 445 in open_ports:
            result['设备类型推测'] = 'Windows设备'
        else:
            result['设备类型推测'] = '未知设备'

    # 3. ARP检测
    mac_address = check_arp_table(ip)
    if mac_address:
        result['MAC地址'] = mac_address
        if result['连接状态'] == '无连接':
            result['连接状态'] = '物理连接存在'

    # 4. 综合判断连接状态
    if result['Ping状态'] == '在线' or open_ports or mac_address:
        if result['连接状态'] == '无连接':
            result['连接状态'] = '设备存在'

    return result

def scan_network_range():
    """扫描网络范围***********-128"""
    print("开始综合网络检测: ***********-128")
    print("检测项目: Ping测试 + 端口扫描 + ARP检测")
    print("请稍候...")

    # 生成IP列表
    ip_list = [f"10.70.230.{i}" for i in range(1, 128)]
    results = []

    # 使用多线程并发检测
    with ThreadPoolExecutor(max_workers=20) as executor:
        futures = [executor.submit(comprehensive_network_check, ip) for ip in ip_list]

        for i, future in enumerate(futures, 1):
            result = future.result()
            results.append(result)

            # 显示进度
            progress = (i / len(ip_list)) * 100

            # 根据检测结果显示状态
            if result['Ping状态'] == '在线':
                status = "✓"
                status_text = f"在线 [{result['连接状态']}]"
            elif result['开放端口']:
                status = "◐"
                status_text = f"有服务 [端口:{result['开放端口']}]"
            elif result['MAC地址']:
                status = "◑"
                status_text = f"物理连接 [MAC:{result['MAC地址'][:8]}...]"
            else:
                status = "✗"
                status_text = "无连接"

            print(f"[{progress:5.1f}%] {status} {result['IP地址']} - {status_text}")

    return results

def save_results_to_csv(results):
    """保存结果到CSV文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"network_scan_results_{timestamp}.csv"

    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['IP地址', 'Ping状态', 'Ping响应时间(ms)', '开放端口', 'MAC地址', '设备类型推测', '连接状态', '扫描时间']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for result in results:
                writer.writerow(result)

        print(f"\n结果已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return None

def print_summary(results):
    """打印扫描摘要"""
    total = len(results)
    ping_online = sum(1 for r in results if r['Ping状态'] == '在线')
    has_ports = sum(1 for r in results if r['开放端口'])
    has_mac = sum(1 for r in results if r['MAC地址'])
    no_connection = sum(1 for r in results if r['连接状态'] == '无连接')

    print("\n" + "="*60)
    print("网络检测结果摘要:")
    print("="*60)
    print(f"总计IP数量: {total}")
    print(f"Ping响应: {ping_online} ({ping_online/total*100:.1f}%)")
    print(f"有开放端口: {has_ports} ({has_ports/total*100:.1f}%)")
    print(f"检测到MAC: {has_mac} ({has_mac/total*100:.1f}%)")
    print(f"无任何连接: {no_connection} ({no_connection/total*100:.1f}%)")

    # 分类显示设备
    categories = {
        '完全在线设备': [r for r in results if r['Ping状态'] == '在线'],
        '有服务但不响应Ping': [r for r in results if r['Ping状态'] != '在线' and r['开放端口']],
        '仅物理连接': [r for r in results if r['Ping状态'] != '在线' and not r['开放端口'] and r['MAC地址']],
        '推测设备类型': [r for r in results if r['设备类型推测']]
    }

    for category, devices in categories.items():
        if devices:
            print(f"\n{category} ({len(devices)}个):")
            for device in devices[:10]:  # 最多显示10个
                if category == '推测设备类型':
                    print(f"  {device['IP地址']} - {device['设备类型推测']}")
                else:
                    extra_info = ""
                    if device['开放端口']:
                        extra_info += f" [端口:{device['开放端口']}]"
                    if device['MAC地址']:
                        extra_info += f" [MAC:{device['MAC地址']}]"
                    print(f"  {device['IP地址']}{extra_info}")

            if len(devices) > 10:
                print(f"  ... 还有{len(devices)-10}个设备")

    # 网线连接建议
    print(f"\n" + "="*60)
    print("网线连接状态分析:")
    print("="*60)

    likely_connected = ping_online + has_ports + has_mac
    likely_disconnected = total - likely_connected

    print(f"可能已连接网线: {likely_connected} ({likely_connected/total*100:.1f}%)")
    print(f"可能未连接网线: {likely_disconnected} ({likely_disconnected/total*100:.1f}%)")

    if likely_disconnected > 0:
        print(f"\n建议检查以下IP对应的网口是否插入网线:")
        disconnected_ips = [r['IP地址'] for r in results if r['连接状态'] == '无连接']
        for i, ip in enumerate(disconnected_ips[:20], 1):  # 最多显示20个
            if i % 8 == 0:
                print(ip)
            else:
                print(f"{ip:<15}", end=" ")
        if len(disconnected_ips) > 20:
            print(f"\n... 还有{len(disconnected_ips)-20}个IP")
        elif len(disconnected_ips) % 8 != 0:
            print()

def main():
    """主函数"""
    print("网络连接状态综合检测工具")
    print("="*60)
    print("检测范围: ***********-128")
    print("检测方法: Ping + 端口扫描 + ARP检测")
    print("目标: 识别真实的网线连接状态")
    print("="*60)

    try:
        start_time = time.time()

        # 执行扫描
        results = scan_network_range()

        # 打印摘要
        print_summary(results)

        # 保存到CSV
        csv_file = save_results_to_csv(results)

        end_time = time.time()
        print(f"\n检测完成，总耗时: {end_time - start_time:.2f}秒")

        if csv_file:
            print(f"详细报告已保存: {csv_file}")
            print("\n说明:")
            print("- Ping响应: 设备在线且响应ping")
            print("- 有开放端口: 设备运行服务但可能禁ping")
            print("- 检测到MAC: 物理连接存在，设备可能关机")
            print("- 无任何连接: 很可能网线未插或设备不存在")

    except KeyboardInterrupt:
        print("\n检测被用户中断")
    except Exception as e:
        print(f"检测过程出错: {e}")

if __name__ == "__main__":
    main()
