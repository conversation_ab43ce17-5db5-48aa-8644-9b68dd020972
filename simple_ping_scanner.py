#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版网络Ping扫描工具
快速扫描***********-128并生成CSV报告
"""

import subprocess
import csv
import time
from datetime import datetime
import os
from concurrent.futures import ThreadPoolExecutor

def ping_ip(ip, timeout=3):
    """
    ping单个IP地址
    
    Args:
        ip (str): IP地址
        timeout (int): 超时时间(秒)
    
    Returns:
        dict: ping结果
    """
    try:
        # 根据操作系统选择ping命令
        if os.name == 'nt':  # Windows
            cmd = ['ping', '-n', '1', '-w', str(timeout * 1000), ip]
        else:  # Linux/Unix
            cmd = ['ping', '-c', '1', '-W', str(timeout), ip]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 1)
        end_time = time.time()
        
        response_time = round((end_time - start_time) * 1000, 2)
        
        if result.returncode == 0:
            return {
                'IP地址': ip,
                '状态': '在线',
                '响应时间(ms)': response_time,
                '扫描时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        else:
            return {
                'IP地址': ip,
                '状态': '离线',
                '响应时间(ms)': 0,
                '扫描时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    except:
        return {
            'IP地址': ip,
            '状态': '超时',
            '响应时间(ms)': 0,
            '扫描时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

def scan_network_range():
    """扫描网络范围***********-128"""
    print("开始扫描网络范围: ***********-128")
    print("请稍候...")
    
    # 生成IP列表
    ip_list = [f"10.70.230.{i}" for i in range(1, 129)]
    results = []
    
    # 使用多线程并发ping
    with ThreadPoolExecutor(max_workers=30) as executor:
        futures = [executor.submit(ping_ip, ip) for ip in ip_list]
        
        for i, future in enumerate(futures, 1):
            result = future.result()
            results.append(result)
            
            # 显示进度
            progress = (i / len(ip_list)) * 100
            status = "✓" if result['状态'] == '在线' else "✗"
            print(f"[{progress:5.1f}%] {status} {result['IP地址']} - {result['状态']}")
    
    return results

def save_results_to_csv(results):
    """保存结果到CSV文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"ping_scan_results_{timestamp}.csv"
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['IP地址', '状态', '响应时间(ms)', '扫描时间']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in results:
                writer.writerow(result)
        
        print(f"\n结果已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return None

def print_summary(results):
    """打印扫描摘要"""
    total = len(results)
    online = sum(1 for r in results if r['状态'] == '在线')
    offline = sum(1 for r in results if r['状态'] == '离线')
    timeout = sum(1 for r in results if r['状态'] == '超时')
    
    print("\n" + "="*50)
    print("扫描结果摘要:")
    print("="*50)
    print(f"总计IP: {total}")
    print(f"在线: {online} ({online/total*100:.1f}%)")
    print(f"离线: {offline} ({offline/total*100:.1f}%)")
    print(f"超时: {timeout} ({timeout/total*100:.1f}%)")
    
    # 显示在线的IP
    online_ips = [r['IP地址'] for r in results if r['状态'] == '在线']
    if online_ips:
        print(f"\n在线主机列表 ({len(online_ips)}个):")
        for ip in online_ips:
            print(f"  {ip}")

def main():
    """主函数"""
    print("简化版网络Ping扫描工具")
    print("="*50)
    
    try:
        start_time = time.time()
        
        # 执行扫描
        results = scan_network_range()
        
        # 打印摘要
        print_summary(results)
        
        # 保存到CSV
        csv_file = save_results_to_csv(results)
        
        end_time = time.time()
        print(f"\n扫描完成，耗时: {end_time - start_time:.2f}秒")
        
        if csv_file:
            print(f"CSV报告: {csv_file}")
            
    except KeyboardInterrupt:
        print("\n扫描被用户中断")
    except Exception as e:
        print(f"扫描出错: {e}")

if __name__ == "__main__":
    main()
