#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络Ping扫描工具
扫描指定IP范围并生成CSV报告
"""

import subprocess
import csv
import threading
import time
from datetime import datetime
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed

class NetworkPingScanner:
    def __init__(self, base_ip="10.70.230", start_host=1, end_host=128, timeout=3):
        """
        初始化网络扫描器
        
        Args:
            base_ip (str): 基础IP地址 (例如: "10.70.230")
            start_host (int): 起始主机号
            end_host (int): 结束主机号
            timeout (int): ping超时时间(秒)
        """
        self.base_ip = base_ip
        self.start_host = start_host
        self.end_host = end_host
        self.timeout = timeout
        self.results = []
        self.lock = threading.Lock()
        
    def ping_host(self, ip):
        """
        ping单个主机
        
        Args:
            ip (str): 要ping的IP地址
            
        Returns:
            dict: 包含ping结果的字典
        """
        try:
            # Windows系统使用ping命令
            if os.name == 'nt':
                cmd = ['ping', '-n', '1', '-w', str(self.timeout * 1000), ip]
            else:
                # Linux/Unix系统使用ping命令
                cmd = ['ping', '-c', '1', '-W', str(self.timeout), ip]
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout + 2)
            end_time = time.time()
            
            response_time = round((end_time - start_time) * 1000, 2)  # 转换为毫秒
            
            if result.returncode == 0:
                # 解析响应时间
                if os.name == 'nt':
                    # Windows ping输出解析
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'time=' in line or '时间=' in line:
                            try:
                                if 'time=' in line:
                                    time_part = line.split('time=')[1].split('ms')[0]
                                else:
                                    time_part = line.split('时间=')[1].split('ms')[0]
                                response_time = float(time_part)
                                break
                            except:
                                pass
                else:
                    # Linux ping输出解析
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'time=' in line:
                            try:
                                time_part = line.split('time=')[1].split(' ')[0]
                                response_time = float(time_part)
                                break
                            except:
                                pass
                
                return {
                    'ip': ip,
                    'status': 'Success',
                    'response_time_ms': response_time,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'error': ''
                }
            else:
                return {
                    'ip': ip,
                    'status': 'Failed',
                    'response_time_ms': 0,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'error': 'Host unreachable'
                }
                
        except subprocess.TimeoutExpired:
            return {
                'ip': ip,
                'status': 'Timeout',
                'response_time_ms': 0,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'error': f'Timeout after {self.timeout} seconds'
            }
        except Exception as e:
            return {
                'ip': ip,
                'status': 'Error',
                'response_time_ms': 0,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'error': str(e)
            }
    
    def scan_network(self, max_workers=50):
        """
        扫描网络范围
        
        Args:
            max_workers (int): 最大并发线程数
        """
        print(f"开始扫描网络范围: {self.base_ip}.{self.start_host}-{self.end_host}")
        print(f"超时设置: {self.timeout}秒")
        print(f"并发线程数: {max_workers}")
        print("-" * 50)
        
        # 生成IP列表
        ip_list = [f"{self.base_ip}.{i}" for i in range(self.start_host, self.end_host + 1)]
        
        # 使用线程池进行并发ping
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_ip = {executor.submit(self.ping_host, ip): ip for ip in ip_list}
            
            # 收集结果
            completed = 0
            total = len(ip_list)
            
            for future in as_completed(future_to_ip):
                result = future.result()
                
                with self.lock:
                    self.results.append(result)
                    completed += 1
                
                # 显示进度
                progress = (completed / total) * 100
                status_symbol = "✓" if result['status'] == 'Success' else "✗"
                print(f"[{progress:6.1f}%] {status_symbol} {result['ip']} - {result['status']}")
        
        # 按IP地址排序结果
        self.results.sort(key=lambda x: int(x['ip'].split('.')[-1]))
        
        print("-" * 50)
        print(f"扫描完成! 总计: {total}, 成功: {sum(1 for r in self.results if r['status'] == 'Success')}")
    
    def save_to_csv(self, filename=None):
        """
        将结果保存到CSV文件
        
        Args:
            filename (str): CSV文件名，如果为None则自动生成
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ping_scan_results_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['ip', 'status', 'response_time_ms', 'timestamp', 'error']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                # 写入表头
                writer.writeheader()
                
                # 写入数据
                for result in self.results:
                    writer.writerow(result)
            
            print(f"结果已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"保存CSV文件时出错: {e}")
            return None
    
    def print_summary(self):
        """打印扫描结果摘要"""
        if not self.results:
            print("没有扫描结果")
            return
        
        total = len(self.results)
        success = sum(1 for r in self.results if r['status'] == 'Success')
        failed = sum(1 for r in self.results if r['status'] == 'Failed')
        timeout = sum(1 for r in self.results if r['status'] == 'Timeout')
        error = sum(1 for r in self.results if r['status'] == 'Error')
        
        print("\n" + "=" * 50)
        print("扫描结果摘要:")
        print("=" * 50)
        print(f"总计IP数量: {total}")
        print(f"成功响应: {success} ({success/total*100:.1f}%)")
        print(f"无响应: {failed} ({failed/total*100:.1f}%)")
        print(f"超时: {timeout} ({timeout/total*100:.1f}%)")
        print(f"错误: {error} ({error/total*100:.1f}%)")
        
        # 显示成功的IP列表
        success_ips = [r['ip'] for r in self.results if r['status'] == 'Success']
        if success_ips:
            print(f"\n活跃主机 ({len(success_ips)}个):")
            for i, ip in enumerate(success_ips, 1):
                if i % 8 == 0:  # 每行显示8个IP
                    print(ip)
                else:
                    print(f"{ip:<15}", end=" ")
            if len(success_ips) % 8 != 0:
                print()  # 换行

def main():
    """主函数"""
    print("网络Ping扫描工具")
    print("=" * 50)
    
    # 创建扫描器实例
    scanner = NetworkPingScanner(
        base_ip="10.70.230",
        start_host=1,
        end_host=128,
        timeout=3
    )
    
    try:
        # 执行扫描
        start_time = time.time()
        scanner.scan_network(max_workers=50)
        end_time = time.time()
        
        # 打印摘要
        scanner.print_summary()
        
        # 保存结果
        csv_file = scanner.save_to_csv()
        
        print(f"\n扫描耗时: {end_time - start_time:.2f}秒")
        
        if csv_file:
            print(f"CSV报告已生成: {csv_file}")
        
    except KeyboardInterrupt:
        print("\n\n扫描被用户中断")
    except Exception as e:
        print(f"\n扫描过程中出现错误: {e}")

if __name__ == "__main__":
    main()
