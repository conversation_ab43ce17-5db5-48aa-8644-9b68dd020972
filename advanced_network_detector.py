#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级网络连接检测工具
专门用于检测网线物理连接状态和设备存在性
结合多种检测方法提供更准确的网络状态判断
"""

import subprocess
import csv
import time
import socket
import struct
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import os

class AdvancedNetworkDetector:
    def __init__(self, base_ip="10.70.230", start_host=1, end_host=128):
        self.base_ip = base_ip
        self.start_host = start_host
        self.end_host = end_host
        self.results = []
        self.lock = threading.Lock()
        
    def ping_test(self, ip, timeout=2):
        """标准ping测试"""
        try:
            if os.name == 'nt':
                cmd = ['ping', '-n', '1', '-w', str(timeout * 1000), ip]
            else:
                cmd = ['ping', '-c', '1', '-W', str(timeout), ip]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 1)
            return result.returncode == 0
        except:
            return False
    
    def tcp_connect_scan(self, ip, ports=[22, 23, 53, 80, 135, 139, 443, 445, 993, 3389], timeout=1):
        """TCP连接扫描"""
        open_ports = []
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(timeout)
                result = sock.connect_ex((ip, port))
                if result == 0:
                    open_ports.append(port)
                sock.close()
            except:
                pass
        return open_ports
    
    def udp_probe(self, ip, ports=[53, 67, 68, 123, 161], timeout=1):
        """UDP探测"""
        responding_ports = []
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.settimeout(timeout)
                sock.sendto(b'\x00', (ip, port))
                try:
                    data, addr = sock.recvfrom(1024)
                    responding_ports.append(port)
                except socket.timeout:
                    pass
                sock.close()
            except:
                pass
        return responding_ports
    
    def arp_detection(self, ip):
        """ARP表检测和主动ARP请求"""
        mac_address = ""
        
        # 1. 检查系统ARP表
        try:
            if os.name == 'nt':
                result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=3)
            else:
                result = subprocess.run(['arp', '-n'], capture_output=True, text=True, timeout=3)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if ip in line:
                        parts = line.split()
                        for part in parts:
                            if '-' in part and len(part) == 17:  # Windows格式
                                mac_address = part
                                break
                            elif ':' in part and len(part) == 17:  # Linux格式
                                mac_address = part
                                break
        except:
            pass
        
        # 2. 主动发送ARP请求 (仅在Windows上尝试)
        if not mac_address and os.name == 'nt':
            try:
                # 发送ping来触发ARP
                subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                             capture_output=True, timeout=2)
                # 再次检查ARP表
                result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=2)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if ip in line:
                            parts = line.split()
                            for part in parts:
                                if '-' in part and len(part) == 17:
                                    mac_address = part
                                    break
            except:
                pass
        
        return mac_address
    
    def wake_on_lan_probe(self, ip):
        """尝试唤醒检测（发送特殊数据包）"""
        try:
            # 发送广播包到该网段，可能触发设备响应
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            sock.settimeout(1)
            
            # 发送魔术包格式的数据
            magic_packet = b'\xff' * 6 + b'\x00' * 100
            sock.sendto(magic_packet, (ip, 9))  # WOL端口
            sock.close()
            return True
        except:
            return False
    
    def comprehensive_detection(self, ip):
        """综合检测单个IP"""
        result = {
            'IP地址': ip,
            'Ping响应': False,
            'TCP端口': '',
            'UDP端口': '',
            'MAC地址': '',
            '设备状态': '无设备',
            '连接状态': '未连接',
            '设备类型': '',
            '可信度': 0,
            '检测时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 1. Ping测试
        ping_success = self.ping_test(ip)
        result['Ping响应'] = ping_success
        if ping_success:
            result['可信度'] += 40
        
        # 2. TCP端口扫描
        tcp_ports = self.tcp_connect_scan(ip)
        if tcp_ports:
            result['TCP端口'] = ','.join(map(str, tcp_ports))
            result['可信度'] += 30
            
            # 设备类型推测
            if 3389 in tcp_ports:
                result['设备类型'] = 'Windows远程桌面'
            elif 22 in tcp_ports:
                result['设备类型'] = 'SSH服务器'
            elif 80 in tcp_ports or 443 in tcp_ports:
                result['设备类型'] = 'Web服务器'
            elif 135 in tcp_ports or 445 in tcp_ports:
                result['设备类型'] = 'Windows文件共享'
            elif 23 in tcp_ports:
                result['设备类型'] = 'Telnet设备'
        
        # 3. UDP探测
        udp_ports = self.udp_probe(ip)
        if udp_ports:
            result['UDP端口'] = ','.join(map(str, udp_ports))
            result['可信度'] += 15
        
        # 4. ARP检测
        mac_address = self.arp_detection(ip)
        if mac_address:
            result['MAC地址'] = mac_address
            result['可信度'] += 25
        
        # 5. 综合判断设备状态
        if result['可信度'] >= 40:
            result['设备状态'] = '设备在线'
            result['连接状态'] = '已连接'
        elif result['可信度'] >= 25:
            result['设备状态'] = '设备存在'
            result['连接状态'] = '可能已连接'
        elif result['可信度'] >= 15:
            result['设备状态'] = '设备可能存在'
            result['连接状态'] = '连接状态不明'
        else:
            result['设备状态'] = '无设备'
            result['连接状态'] = '未连接'
        
        return result
    
    def scan_network(self, max_workers=25):
        """扫描整个网络范围"""
        print(f"开始高级网络检测: {self.base_ip}.{self.start_host}-{self.end_host}")
        print("检测方法: Ping + TCP扫描 + UDP探测 + ARP检测")
        print("-" * 70)
        
        ip_list = [f"{self.base_ip}.{i}" for i in range(self.start_host, self.end_host + 1)]
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_ip = {executor.submit(self.comprehensive_detection, ip): ip for ip in ip_list}
            
            completed = 0
            total = len(ip_list)
            
            for future in as_completed(future_to_ip):
                result = future.result()
                
                with self.lock:
                    self.results.append(result)
                    completed += 1
                
                # 显示进度
                progress = (completed / total) * 100
                confidence = result['可信度']
                
                if confidence >= 40:
                    status = "✓"
                    status_text = f"在线 [{result['设备状态']}]"
                elif confidence >= 25:
                    status = "◐"
                    status_text = f"存在 [可信度:{confidence}%]"
                elif confidence >= 15:
                    status = "◑"
                    status_text = f"可能存在 [可信度:{confidence}%]"
                else:
                    status = "✗"
                    status_text = "无设备"
                
                print(f"[{progress:5.1f}%] {status} {result['IP地址']} - {status_text}")
        
        # 按IP排序
        self.results.sort(key=lambda x: int(x['IP地址'].split('.')[-1]))
        print("-" * 70)
        print(f"检测完成! 发现设备: {sum(1 for r in self.results if r['可信度'] >= 25)}")
    
    def save_to_csv(self, filename=None):
        """保存结果到CSV"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"advanced_network_scan_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['IP地址', 'Ping响应', 'TCP端口', 'UDP端口', 'MAC地址', 
                            '设备状态', '连接状态', '设备类型', '可信度', '检测时间']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in self.results:
                    writer.writerow(result)
            
            print(f"详细报告已保存: {filename}")
            return filename
        except Exception as e:
            print(f"保存文件出错: {e}")
            return None
    
    def print_analysis(self):
        """打印分析结果"""
        if not self.results:
            return
        
        total = len(self.results)
        online_devices = [r for r in self.results if r['可信度'] >= 40]
        existing_devices = [r for r in self.results if 25 <= r['可信度'] < 40]
        possible_devices = [r for r in self.results if 15 <= r['可信度'] < 25]
        no_devices = [r for r in self.results if r['可信度'] < 15]
        
        print("\n" + "="*70)
        print("高级网络检测分析报告")
        print("="*70)
        print(f"检测范围: {self.base_ip}.{self.start_host}-{self.end_host} (共{total}个IP)")
        print(f"设备在线: {len(online_devices)} ({len(online_devices)/total*100:.1f}%)")
        print(f"设备存在: {len(existing_devices)} ({len(existing_devices)/total*100:.1f}%)")
        print(f"可能有设备: {len(possible_devices)} ({len(possible_devices)/total*100:.1f}%)")
        print(f"确认无设备: {len(no_devices)} ({len(no_devices)/total*100:.1f}%)")
        
        # 网线连接建议
        likely_connected = len(online_devices) + len(existing_devices)
        likely_disconnected = len(no_devices)
        uncertain = len(possible_devices)
        
        print(f"\n网线连接状态评估:")
        print(f"很可能已连接: {likely_connected} ({likely_connected/total*100:.1f}%)")
        print(f"状态不确定: {uncertain} ({uncertain/total*100:.1f}%)")
        print(f"很可能未连接: {likely_disconnected} ({likely_disconnected/total*100:.1f}%)")
        
        # 显示详细设备信息
        if online_devices:
            print(f"\n在线设备详情:")
            for device in online_devices:
                extra_info = []
                if device['设备类型']:
                    extra_info.append(f"类型:{device['设备类型']}")
                if device['MAC地址']:
                    extra_info.append(f"MAC:{device['MAC地址']}")
                if device['TCP端口']:
                    extra_info.append(f"TCP:{device['TCP端口']}")
                
                info_str = " | ".join(extra_info) if extra_info else "基本信息"
                print(f"  {device['IP地址']} - {info_str}")

def main():
    detector = AdvancedNetworkDetector()
    
    try:
        start_time = time.time()
        detector.scan_network()
        detector.print_analysis()
        detector.save_to_csv()
        
        end_time = time.time()
        print(f"\n总检测时间: {end_time - start_time:.2f}秒")
        
    except KeyboardInterrupt:
        print("\n检测被中断")
    except Exception as e:
        print(f"检测出错: {e}")

if __name__ == "__main__":
    main()
